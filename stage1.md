几个问题：
1. outputs/retrain_results 是scripts/retrain_with_enhanced_features.py生成的，发现只有一个模型文件，而不像 run_pipeline.py 生成的 outputs/0.1.0/里面有好几个文件，比如 binning_results.pkl 等等
   1. 这样对吗？
   2. retrain_with_enhanced_features.py 和 run_pipeline.py 是不是本质上只有使用的训练样本不一样？如果是的话是不是应该共享很多代码，只是简单保持两个入口，然后 retrain_with_enhanced_features 可以保持 run_pipeline在output 的输出风格， 比如 输出<version>_enhanced 这样的方式，这样容易区分
2. model_monitor.py 是不是直接从 outputs/ 获取最近的两个版本自动对比更清晰简单。或者我看到你有使用到 baseline_performance.json， 是不是可以修改run_pipeline， 每次训练之后在 outputs/<version>/下面生成这个文件，然后 就很容易对比了。目前我看到没有这个数据的时候，是你默认生成了。 另外如果生成  baseline_performance.json 的时候，是不是可以结合 evaluation/ 下面的 evaluation_results.json 和 model_scorecard_compare.csv ,我记得这两个数据也是用来作对比的，好像是给 model_compare_traditional.py用来做对比的。 这块儿对比的逻辑你统一规划一下，让他们更清晰一点。 我觉得可以考虑在 outputs/<version> 下面新增一个 baseline 文件夹用来存放这些。 然后 放一个compare文件来保存所有的对比结果。另外我也不清楚 outputs/<version>/ 下面的 binning ， evaluation, feature,models, visualization 这几个哪个是正常的结果输出，哪些是为了给 model_compare_traditional.py 用的。 给model_compare_traditional用的是不是都放在baseline里面会更清晰一些