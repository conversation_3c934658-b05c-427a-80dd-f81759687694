{"version": "0.1.0_enhanced", "generated_time": "2025-07-12T15:46:03.069286", "overall_comparison": {"model_auc": 0.7002043588394377, "traditional_auc": 0.29650387302830433, "improvement": 0.4037004858111333, "improvement_pct": 136.15352868345028, "winner": "model"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 2000, "traditional_auc": 0.7015941971575579, "model_auc": 0.7750386225291483, "auc_improvement": 0.07344442537159046, "improvement_pct": 10.468220186133737}, "train": {"subset_name": "训练集", "sample_count": 1400, "traditional_auc": 0.7008377499276061, "model_auc": 0.8055733266888001, "auc_improvement": 0.10473557676119405, "improvement_pct": 14.944340080426999}, "test": {"subset_name": "测试集", "sample_count": 600, "traditional_auc": 0.7034961269716957, "model_auc": 0.7002043588394377, "auc_improvement": -0.003291768132258066, "improvement_pct": -0.*****************}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}