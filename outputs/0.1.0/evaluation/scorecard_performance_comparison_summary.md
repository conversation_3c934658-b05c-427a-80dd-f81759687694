# 评分卡性能对比汇总报告

**版本**: 0.1.0  
**生成时间**: 2025-07-12 15:47:37  

## 📊 性能指标对比表格

| 评估对象 | AUC | KS | Accuracy | Precision | Recall | Specificity | F1 | Threshold |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 全量样本-人工分数 | 0.7016 | 0.3175 | 0.6565 | 0.5060 | 0.6662 | 0.6513 | 0.5751 | -57.5200 |
| 全量样本-模型分数 | 0.7732 | 0.4260 | 0.6925 | 0.5412 | 0.7808 | 0.6452 | 0.6393 | 0.3121 |
| 训练集-人工分数 | 0.7008 | 0.3188 | 0.6579 | 0.5078 | 0.6646 | 0.6542 | 0.5757 | -57.5900 |
| 训练集-模型分数 | 0.8018 | 0.4755 | 0.7271 | 0.5824 | 0.7730 | 0.7025 | 0.6643 | 0.3362 |
| 测试集-人工分数 | 0.7035 | 0.3562 | 0.6283 | 0.4809 | 0.8421 | 0.5141 | 0.6122 | -60.8300 |
| 测试集-模型分数 | 0.7035 | 0.3425 | 0.6267 | 0.4790 | 0.8182 | 0.5243 | 0.6042 | 0.2579 |

## 📈 详细分析

### 🎯 核心指标对比

#### 全量样本

- **AUC提升**: +0.0716 (🟢 模型更优)
- **KS值提升**: +0.1085 (🟢 模型更优)
- **样本数量**: 2000 个

#### 训练集

- **AUC提升**: +0.1010 (🟢 模型更优)
- **KS值提升**: +0.1567 (🟢 模型更优)
- **样本数量**: 1400 个

#### 测试集

- **AUC提升**: +0.0000 (🟢 模型更优)
- **KS值提升**: -0.0137 (🔴 传统更优)
- **样本数量**: 600 个

### 💡 结果解释

- **AUC (Area Under Curve)**: 衡量模型整体判别能力，值越大越好
- **KS值**: 衡量模型区分好坏样本的能力，通常0.2-0.6为良好
- **精确率**: 预测为坏的样本中真正为坏的比例
- **召回率**: 真正为坏的样本中被正确识别的比例
- **F1分数**: 精确率和召回率的调和平均数

### 🚀 优化建议

- ✅ 模型在训练集上表现最佳
- 📊 建议重点关注模型在实际业务中的应用效果

---
*报告由 compare_scorecard_performance.py 自动生成*
