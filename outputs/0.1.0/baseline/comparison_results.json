{"version": "0.1.0", "generated_time": "2025-07-12T15:45:54.769877", "overall_comparison": {"model_auc": 0.7035450751967106, "traditional_auc": 0.29650387302830433, "improvement": 0.4070412021684063, "improvement_pct": 137.28023111844817, "winner": "model"}, "subset_analysis": {"all": {"subset_name": "全量样本", "sample_count": 2000, "traditional_auc": 0.7015941971575579, "model_auc": 0.7732252342659959, "auc_improvement": 0.07163103710843799, "improvement_pct": 10.209753358657236}, "train": {"subset_name": "训练集", "sample_count": 1400, "traditional_auc": 0.7008377499276061, "model_auc": 0.8018470006442503, "auc_improvement": 0.10100925071664424, "improvement_pct": 14.***************}, "test": {"subset_name": "测试集", "sample_count": 600, "traditional_auc": 0.7034961269716957, "model_auc": 0.7035450751967106, "auc_improvement": 4.894822501488871e-05, "improvement_pct": 0.0069578528065994405}}, "data_sources": {"evaluation_results": "evaluation/evaluation_results.json", "compare_data": "evaluation/model_scorecard_compare.csv", "feature_weights": "feature/feature_weights.csv", "iv_ranking": "binning/iv_ranking.csv"}, "usage_note": "此文件整合了所有对比相关数据，供对比脚本使用"}