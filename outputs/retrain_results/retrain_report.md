# 模型重训练报告

**生成时间**: 2025-07-12 14:59:29
**使用增强特征**: 是
**特征数量**: 46

## 📊 模型性能对比

| 模型 | 训练准确率 | 测试AUC | 交叉验证AUC | 过拟合程度 |
|------|------------|---------|-------------|------------|
| LogisticRegression | 0.7257 | 0.7256 | 0.7125±0.0169 | 0.0002 |
| RandomForest | 1.0000 | 0.7162 | 0.6937±0.0126 | 0.2838 |

## 🏆 最佳模型: LogisticRegression

- **测试AUC**: 0.7256
- **交叉验证AUC**: 0.7125 ± 0.0169
- **过拟合程度**: 0.0002

## 🔍 特征重要性分析

### LogisticRegression (系数绝对值)

| 排名 | 特征名称 | 重要性 |
|------|----------|--------|
| 36 | financial_ratio_debt | 0.5896 |
| 8 | debt_market_interaction | 0.4076 |
| 43 | growth_market_interaction | 0.3651 |
| 38 | financial_ratio_current | 0.3338 |
| 34 | financial_ratio_profit | 0.2815 |
| 21 | operation_management_efficiency | 0.2197 |
| 9 | external_competitive_pressure | 0.1857 |
| 33 | financial_cash_flow | 0.1815 |
| 1 | risk_resistance | 0.1758 |
| 28 | operation_market_share | 0.1575 |
| 2 | relative_market_position | 0.1575 |
| 12 | external_technology_adaptation | 0.1479 |
| 14 | external_environmental_impact | 0.1400 |
| 29 | financial_stability_ebitda | 0.1340 |
| 22 | operation_brand_value | 0.1217 |

### RandomForest (Gini重要性)

| 排名 | 特征名称 | 重要性 |
|------|----------|--------|
| 34 | financial_ratio_profit | 0.0368 |
| 46 | enterprise_health_score | 0.0357 |
| 38 | financial_ratio_current | 0.0333 |
| 1 | risk_resistance | 0.0333 |
| 43 | growth_market_interaction | 0.0286 |
| 44 | financial_strength | 0.0281 |
| 33 | financial_cash_flow | 0.0263 |
| 21 | operation_management_efficiency | 0.0263 |
| 30 | financial_growth_revenue | 0.0253 |
| 37 | financial_ratio_quick | 0.0247 |
| 29 | financial_stability_ebitda | 0.0238 |
| 6 | liquidity_risk | 0.0232 |
| 7 | innovation_team_synergy | 0.0218 |
| 15 | external_regulatory_compliance | 0.0217 |
| 45 | innovation_drive_index | 0.0215 |

## 💡 优化建议

- 📈 **性能提升**: 考虑更多特征工程或尝试其他算法
- ✅ **特征工程有效**: 增强特征提升了模型性能

---
*报告由 retrain_with_enhanced_features.py 自动生成*
