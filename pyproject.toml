[project]
name = "innovation-model-investigation"
version = "0.1.0_enhanced"
description = "企业风险评估模型权重优化项目"
readme = "README.md"
requires-python = ">=3.8"
dependencies = [ "pandas>=2.0.0", "numpy>=1.24.0", "scikit-learn>=1.3.0", "scipy>=1.11.0", "optbinning>=0.17.0", "scorecardpy>=*******", "matplotlib>=3.7.0", "seaborn>=0.12.0", "plotly>=5.15.0", "shap>=0.42.0", "jupyter>=1.0.0", "pytest>=7.0.0", "pydantic>=2.0.0",]
[[project.authors]]
name = "Your Name"
email = "<EMAIL>"

[build-system]
requires = [ "setuptools", "wheel",]
build-backend = "setuptools.build_meta"

[project.optional-dependencies]
dev = [ "black", "isort", "flake8", "mypy",]

[tool.setuptools.package-dir]
"" = "src"

[tool.setuptools.packages.find]
where = [ "src",]
