#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据分析工具函数
提供共享的数据分析功能，供其他脚本复用
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple


def load_data(filepath: str = 'data/enterprise_risk_sample_data.csv') -> pd.DataFrame:
    """加载数据文件"""
    try:
        df = pd.read_csv(filepath)
        print(f"数据加载成功，共 {len(df)} 条记录，{len(df.columns)} 个特征")
        return df
    except Exception as e:
        print(f"数据加载失败: {e}")
        raise


def analyze_label_distribution(df: pd.DataFrame) -> Dict:
    """分析标签分布"""
    label_counts = df['label'].value_counts()
    label_props = df['label'].value_counts(normalize=True)
    
    return {
        'counts': label_counts,
        'proportions': label_props,
        'good_ratio': label_props[0] if 0 in label_props else 0,
        'bad_ratio': label_props[1] if 1 in label_props else 0
    }


def calculate_feature_correlations(df: pd.DataFrame, 
                                 feature_list: List[str] = None) -> List[Tuple[str, float]]:
    """计算特征与标签的相关性"""
    if feature_list is None:
        feature_list = [col for col in df.columns if col != 'label']
    
    correlations = []
    for feature in feature_list:
        if feature in df.columns:
            corr = np.corrcoef(df[feature], df['label'])[0,1]
            correlations.append((feature, corr))
    
    return correlations


def analyze_risk_stratification(df: pd.DataFrame, 
                              risk_score_col: str = 'comprehensive_risk_score') -> pd.DataFrame:
    """分析风险分层"""
    if risk_score_col not in df.columns:
        raise ValueError(f"未找到风险得分字段: {risk_score_col}")
    
    # 创建风险分层
    df_copy = df.copy()
    df_copy['risk_level'] = pd.cut(df_copy[risk_score_col], 
                                  bins=[0, 35, 45, 55, 65, 75, 100],
                                  labels=['极高风险', '高风险', '中等风险', 
                                         '较低风险', '低风险', '极低风险'])
    
    # 统计分析
    risk_analysis = df_copy.groupby('risk_level', observed=False)['label'].agg(['count', 'mean']).round(3)
    risk_analysis.columns = ['样本数', '坏企业比例']
    
    return risk_analysis


def check_data_quality(df: pd.DataFrame) -> Dict:
    """检查数据质量"""
    # 缺失值检查
    missing_counts = df.isnull().sum()
    missing_data = missing_counts[missing_counts > 0]
    
    # 数值特征统计
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    
    # 异常值检查
    outliers = {}
    for col in numeric_cols:
        if col != 'label':
            max_val = df[col].abs().max()
            if max_val > 10:
                outliers[col] = max_val
    
    return {
        'missing_data': missing_data,
        'numeric_features_count': len(numeric_cols),
        'outliers': outliers
    }


def categorize_features(df: pd.DataFrame) -> Dict[str, List[str]]:
    """按类别分组特征"""
    financial_cols = [col for col in df.columns if 'financial' in col]
    operation_cols = [col for col in df.columns if 'operation' in col]
    external_cols = [col for col in df.columns if 'external' in col]
    
    return {
        'financial': financial_cols,
        'operation': operation_cols,
        'external': external_cols
    }


def evaluate_model_potential(correlations: List[Tuple[str, float]], 
                           risk_score_corr: float) -> Dict:
    """评估模型性能潜力"""
    max_corr = max([abs(corr) for _, corr in correlations]) if correlations else 0
    strong_features = [name for name, corr in correlations if abs(corr) > 0.3]
    
    # 预测AUC范围
    if abs(risk_score_corr) > 0.5:
        auc_prediction = "优秀 (AUC > 0.75)"
        performance_level = "excellent"
    elif abs(risk_score_corr) > 0.3:
        auc_prediction = "良好 (AUC > 0.65)"
        performance_level = "good"
    elif abs(risk_score_corr) > 0.2:
        auc_prediction = "一般 (AUC > 0.60)"
        performance_level = "fair"
    else:
        auc_prediction = "较差 (AUC < 0.60)"
        performance_level = "poor"
    
    return {
        'max_correlation': max_corr,
        'strong_features': strong_features,
        'strong_features_count': len(strong_features),
        'risk_score_correlation': abs(risk_score_corr),
        'auc_prediction': auc_prediction,
        'performance_level': performance_level
    }


def print_correlation_analysis(correlations: List[Tuple[str, float]], 
                             category_name: str, 
                             max_display: int = 10):
    """打印相关性分析结果"""
    print(f"{category_name}与标签的相关性 (共{len(correlations)}个):")
    for feature, corr in correlations[:max_display]:
        print(f"  {feature}: {corr:.4f}")


def print_separator(title: str, width: int = 50):
    """打印分隔符"""
    print("\n" + "="*width)
    print(title)
    print("="*width) 