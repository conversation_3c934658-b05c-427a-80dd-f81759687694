#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一模型监控系统
自动对比 outputs/ 目录下最近的两个版本，生成完整的对比报告
解决多个对比脚本功能重合的问题
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import glob

class UnifiedModelMonitor:
    """统一模型监控器"""
    
    def __init__(self):
        self.outputs_dir = Path("outputs")
        self.versions = []
        self.baseline_data = {}
        self.current_data = {}
        
    def discover_versions(self):
        """发现所有可用版本"""
        if not self.outputs_dir.exists():
            print("❌ outputs目录不存在！")
            return []
        
        # 获取所有版本目录
        version_dirs = [d.name for d in self.outputs_dir.iterdir() 
                       if d.is_dir() and not d.name.startswith('.')]
        
        # 按版本号排序
        def version_key(v):
            try:
                # 处理 enhanced 版本
                if '_enhanced' in v:
                    base_v = v.replace('_enhanced', '')
                    parts = [int(x) for x in base_v.split('.')]
                    return parts + [1]  # enhanced 版本排在后面
                else:
                    parts = [int(x) for x in v.split('.')]
                    return parts + [0]
            except:
                return [0, 0, 0, 0]
        
        version_dirs.sort(key=version_key, reverse=True)
        self.versions = version_dirs
        
        print(f"📂 发现版本: {', '.join(version_dirs)}")
        return version_dirs
    
    def load_version_data(self, version):
        """加载指定版本的数据"""
        version_dir = self.outputs_dir / version
        data = {
            'version': version,
            'evaluation_results': None,
            'compare_data': None,
            'baseline_metrics': None
        }
        
        # 1. 加载评估结果
        eval_path = version_dir / "evaluation" / "evaluation_results.json"
        if eval_path.exists():
            with open(eval_path, 'r') as f:
                data['evaluation_results'] = json.load(f)
        
        # 2. 加载对比数据
        compare_path = version_dir / "evaluation" / "model_scorecard_compare.csv"
        if compare_path.exists():
            data['compare_data'] = pd.read_csv(compare_path)
        
        # 3. 生成baseline metrics（如果不存在）
        baseline_path = version_dir / "baseline" / "baseline_performance.json"
        if not baseline_path.exists() and data['evaluation_results']:
            self.generate_baseline_metrics(version, data['evaluation_results'])
        
        if baseline_path.exists():
            with open(baseline_path, 'r') as f:
                data['baseline_metrics'] = json.load(f)
        
        return data
    
    def generate_baseline_metrics(self, version, eval_results):
        """为版本生成baseline metrics文件"""
        version_dir = self.outputs_dir / version
        baseline_dir = version_dir / "baseline"
        baseline_dir.mkdir(exist_ok=True)
        
        # 提取关键指标
        baseline_metrics = {
            'version': version,
            'generated_time': datetime.now().isoformat(),
            'auc': eval_results.get('test_auc', 0.75),
            'ks': eval_results.get('test_ks', 0.30),
            'accuracy': eval_results.get('test_accuracy', 0.75),
            'precision': eval_results.get('test_precision', 0.70),
            'recall': eval_results.get('test_recall', 0.65),
            'f1': eval_results.get('test_f1', 0.67)
        }
        
        baseline_path = baseline_dir / "baseline_performance.json"
        with open(baseline_path, 'w') as f:
            json.dump(baseline_metrics, f, indent=2, ensure_ascii=False)
        
        print(f"📄 生成baseline文件: {baseline_path}")
        return baseline_metrics
    
    def compare_versions(self, baseline_version, current_version):
        """对比两个版本"""
        print(f"\n🔍 对比版本: {baseline_version} vs {current_version}")
        
        baseline_data = self.load_version_data(baseline_version)
        current_data = self.load_version_data(current_version)
        
        comparison = {
            'baseline_version': baseline_version,
            'current_version': current_version,
            'metrics_comparison': {},
            'data_drift': {},
            'recommendations': []
        }
        
        # 1. 性能指标对比
        if (baseline_data['evaluation_results'] and 
            current_data['evaluation_results']):
            
            baseline_eval = baseline_data['evaluation_results']
            current_eval = current_data['evaluation_results']
            
            for metric in ['test_auc', 'test_ks', 'test_accuracy', 'test_precision', 'test_recall', 'test_f1']:
                if metric in baseline_eval and metric in current_eval:
                    baseline_val = baseline_eval[metric]
                    current_val = current_eval[metric]
                    diff = current_val - baseline_val
                    diff_pct = (diff / baseline_val * 100) if baseline_val != 0 else 0
                    
                    comparison['metrics_comparison'][metric] = {
                        'baseline': baseline_val,
                        'current': current_val,
                        'diff': diff,
                        'diff_pct': diff_pct,
                        'status': self.get_performance_status(diff_pct)
                    }
        
        # 2. 数据漂移检测（如果有对比数据）
        if (baseline_data['compare_data'] is not None and 
            current_data['compare_data'] is not None):
            
            drift_analysis = self.analyze_data_drift(
                baseline_data['compare_data'], 
                current_data['compare_data']
            )
            comparison['data_drift'] = drift_analysis
        
        # 3. 生成建议
        comparison['recommendations'] = self.generate_recommendations(comparison)
        
        return comparison
    
    def get_performance_status(self, diff_pct):
        """获取性能变化状态"""
        if diff_pct > 5:
            return "🟢 显著提升"
        elif diff_pct > 2:
            return "🟢 轻微提升"
        elif diff_pct > -2:
            return "🔵 基本稳定"
        elif diff_pct > -5:
            return "🟡 轻微下降"
        else:
            return "🔴 显著下降"
    
    def analyze_data_drift(self, baseline_df, current_df):
        """分析数据漂移"""
        drift_results = {}
        
        # 检查样本分布变化
        if 'label' in baseline_df.columns and 'label' in current_df.columns:
            baseline_pos_rate = baseline_df['label'].mean()
            current_pos_rate = current_df['label'].mean()
            
            drift_results['label_distribution'] = {
                'baseline_positive_rate': baseline_pos_rate,
                'current_positive_rate': current_pos_rate,
                'drift': abs(current_pos_rate - baseline_pos_rate)
            }
        
        # 检查模型分数分布
        if 'model_score' in baseline_df.columns and 'model_score' in current_df.columns:
            from scipy import stats
            
            # KS检验
            ks_stat, p_value = stats.ks_2samp(
                baseline_df['model_score'], 
                current_df['model_score']
            )
            
            drift_results['model_score_distribution'] = {
                'ks_statistic': ks_stat,
                'p_value': p_value,
                'significant_drift': p_value < 0.05
            }
        
        return drift_results
    
    def generate_recommendations(self, comparison):
        """生成优化建议"""
        recommendations = []
        
        # 基于性能变化的建议
        metrics_comp = comparison['metrics_comparison']
        
        if any(m['status'].startswith('🔴') for m in metrics_comp.values()):
            recommendations.append("🚨 **立即行动**: 模型性能显著下降，建议立即重训练")
        
        if any(m['status'].startswith('🟡') for m in metrics_comp.values()):
            recommendations.append("⚠️ **密切关注**: 模型性能轻微下降，建议增加监控频率")
        
        if any(m['status'].startswith('🟢') for m in metrics_comp.values()):
            recommendations.append("✅ **性能提升**: 模型性能有所改善，可考虑部署新版本")
        
        # 基于数据漂移的建议
        drift = comparison['data_drift']
        if drift.get('model_score_distribution', {}).get('significant_drift', False):
            recommendations.append("📊 **数据漂移**: 检测到显著的数据分布变化，建议检查数据源")
        
        return recommendations
    
    def generate_monitoring_report(self, comparison):
        """生成监控报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"outputs/unified_monitoring_report_{timestamp}.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 统一模型监控报告\n\n")
            f.write(f"**基线版本**: {comparison['baseline_version']}\n")
            f.write(f"**当前版本**: {comparison['current_version']}\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 性能对比
            f.write("## 📊 性能指标对比\n\n")
            f.write("| 指标 | 基线值 | 当前值 | 变化 | 变化率 | 状态 |\n")
            f.write("|------|--------|--------|------|--------|------|\n")
            
            for metric, data in comparison['metrics_comparison'].items():
                f.write(f"| {metric.upper()} | {data['baseline']:.4f} | {data['current']:.4f} | "
                       f"{data['diff']:+.4f} | {data['diff_pct']:+.1f}% | {data['status']} |\n")
            
            # 数据漂移
            if comparison['data_drift']:
                f.write("\n## 🔍 数据漂移分析\n\n")
                
                drift = comparison['data_drift']
                if 'label_distribution' in drift:
                    label_drift = drift['label_distribution']
                    f.write(f"- **标签分布变化**: {label_drift['baseline_positive_rate']:.3f} → "
                           f"{label_drift['current_positive_rate']:.3f} (漂移: {label_drift['drift']:.3f})\n")
                
                if 'model_score_distribution' in drift:
                    score_drift = drift['model_score_distribution']
                    f.write(f"- **模型分数分布**: KS统计量={score_drift['ks_statistic']:.4f}, "
                           f"p值={score_drift['p_value']:.4f}\n")
                    if score_drift['significant_drift']:
                        f.write("  ⚠️ 检测到显著的分布变化\n")
            
            # 建议措施
            f.write("\n## 💡 建议措施\n\n")
            for rec in comparison['recommendations']:
                f.write(f"- {rec}\n")
            
            f.write("\n---\n*报告由统一模型监控系统自动生成*\n")
        
        print(f"📄 监控报告已保存: {report_path}")
        return report_path

def main():
    """主函数"""
    print("🔍 统一模型监控系统")
    print("=" * 50)
    
    monitor = UnifiedModelMonitor()
    
    # 1. 发现版本
    versions = monitor.discover_versions()
    if len(versions) < 2:
        print("⚠️ 需要至少2个版本才能进行对比")
        return
    
    # 2. 对比最近的两个版本
    current_version = versions[0]
    baseline_version = versions[1]
    
    comparison = monitor.compare_versions(baseline_version, current_version)
    
    # 3. 生成报告
    report_path = monitor.generate_monitoring_report(comparison)
    
    print(f"\n✅ 监控完成！")
    print(f"📊 对比版本: {baseline_version} vs {current_version}")
    print(f"📄 详细报告: {report_path}")

if __name__ == "__main__":
    main()
