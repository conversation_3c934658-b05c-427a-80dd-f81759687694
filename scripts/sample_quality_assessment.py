#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样本质量与建模潜力评估脚本
专注于分析样本数据的关键特征与标签相关性、风险分层单调性、预估AUC等，帮助判断数据是否适合建模及修复效果。
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_analysis_utils import (
    load_data, calculate_feature_correlations, 
    analyze_risk_stratification, evaluate_model_potential
)
import numpy as np

def main():
    output_dir = 'outputs/sample_data/quality/'
    os.makedirs(output_dir, exist_ok=True)
    print("🔍 验证数据修复效果...")
    
    # 加载数据
    df = load_data()
    
    # 1. 检查关键特征与标签的相关性
    print("\n📊 关键特征与标签相关性（修复后）:")
    key_features = [
        'financial_ratio_debt',
        'financial_ratio_current', 
        'financial_cash_flow',
        'financial_growth_revenue',
        'operation_market_share',
        'operation_innovation_capability',
        'external_regulatory_compliance'
    ]
    
    correlations = calculate_feature_correlations(df, key_features)
    for feature, corr in correlations:
        print(f"  {feature}: {corr:.4f}")
    
    # 2. 检查综合风险得分与标签的关系
    risk_label_corr = np.corrcoef(df['comprehensive_risk_score'], df['label'])[0,1]
    print(f"\n🎯 综合风险得分与标签相关性: {risk_label_corr:.4f}")
    
    # 3. 按风险分层分析标签分布
    print("\n📈 按风险分层的标签分布:")
    risk_analysis = analyze_risk_stratification(df)
    print(risk_analysis)
    
    # 4. 评估数据质量改善
    evaluation = evaluate_model_potential(correlations, risk_label_corr)
    
    print(f"\n✅ 数据质量评估:")
    print(f"  - 最强特征相关性: {evaluation['max_correlation']:.4f}")
    print(f"  - 综合风险得分相关性: {evaluation['risk_score_correlation']:.4f}")
    print(f"  - 风险分层是否合理: {'是' if risk_analysis['坏企业比例'].is_monotonic_decreasing else '否'}")
    print(f"  - 强相关特征数量: {evaluation['strong_features_count']}")
    
    if evaluation['strong_features']:
        print(f"  - 强相关特征: {evaluation['strong_features']}")
    
    # 5. 预期模型性能改善
    print(f"\n🚀 预期模型性能改善:")
    performance_icons = {
        'excellent': '🌟',
        'good': '✅', 
        'fair': '⚠️',
        'poor': '❌'
    }
    icon = performance_icons.get(evaluation['performance_level'], '❓')
    print(f"  {icon} {evaluation['auc_prediction']}")
    
    # 6. 修复建议
    if evaluation['performance_level'] in ['poor', 'fair']:
        print(f"\n💡 进一步优化建议:")
        if evaluation['max_correlation'] < 0.3:
            print("  - 增强特征与标签的因果关系")
        if evaluation['risk_score_correlation'] < 0.4:
            print("  - 优化综合风险得分计算逻辑")
        if evaluation['strong_features_count'] == 0:
            print("  - 减少数据生成中的随机噪音")
    else:
        print(f"\n🎉 数据质量已达到{evaluation['performance_level']}水平，可以进行模型训练！")

    print(f"\n所有评估结果已输出到 {output_dir} 目录（如需保存为文件可自行扩展保存逻辑）")

if __name__ == "__main__":
    main() 