#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于 baseline 目录的统一对比分析
专门从 outputs/<version>/baseline/ 目录读取数据进行对比
替代原有的分散对比逻辑，提供清晰统一的对比分析
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import argparse
import matplotlib.pyplot as plt
from sklearn.metrics import roc_curve, roc_auc_score

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

class BaselineComparison:
    """基于 baseline 目录的统一对比分析器"""
    
    def __init__(self):
        self.outputs_dir = Path("outputs")
        self.versions = []
        
    def discover_versions(self):
        """发现所有有 baseline 目录的版本"""
        if not self.outputs_dir.exists():
            print("❌ outputs目录不存在！")
            return []
        
        version_dirs = []
        for d in self.outputs_dir.iterdir():
            if d.is_dir() and not d.name.startswith('.'):
                baseline_dir = d / "baseline"
                if baseline_dir.exists():
                    version_dirs.append(d.name)
        
        # 按版本号排序
        def version_key(v):
            try:
                if '_enhanced' in v:
                    base_v = v.replace('_enhanced', '')
                    parts = [int(x) for x in base_v.split('.')]
                    return parts + [1]
                else:
                    parts = [int(x) for x in v.split('.')]
                    return parts + [0]
            except:
                return [0, 0, 0, 0]
        
        version_dirs.sort(key=version_key, reverse=True)
        self.versions = version_dirs
        
        print(f"📂 发现版本: {', '.join(version_dirs)}")
        return version_dirs
    
    def load_baseline_data(self, version):
        """从 baseline 目录加载完整数据"""
        baseline_dir = self.outputs_dir / version / "baseline"
        
        data = {
            'version': version,
            'baseline_performance': None,
            'comparison_results': None,
            'compare_data': None
        }
        
        # 1. 加载性能基准
        perf_path = baseline_dir / "baseline_performance.json"
        if perf_path.exists():
            with open(perf_path, 'r') as f:
                data['baseline_performance'] = json.load(f)
        
        # 2. 加载对比结果
        comp_path = baseline_dir / "comparison_results.json"
        if comp_path.exists():
            with open(comp_path, 'r') as f:
                data['comparison_results'] = json.load(f)
        
        # 3. 加载对比数据
        csv_path = baseline_dir / "model_scorecard_compare.csv"
        if csv_path.exists():
            data['compare_data'] = pd.read_csv(csv_path)
        
        return data
    
    def analyze_single_version(self, version):
        """分析单个版本的模型 vs 传统方法对比"""
        print(f"\n🔍 版本 {version}: 模型 vs 传统方法详细对比")
        print("-" * 60)
        
        data = self.load_baseline_data(version)
        
        if not data['comparison_results']:
            print("❌ 未找到对比结果数据！")
            return None
        
        comp_results = data['comparison_results']
        overall = comp_results['overall_comparison']
        
        print(f"📊 整体对比:")
        print(f"  模型AUC:     {overall['model_auc']:.4f}")
        print(f"  传统AUC:     {overall['traditional_auc']:.4f}")
        print(f"  AUC提升:     {overall['improvement']:+.4f}")
        print(f"  提升幅度:    {overall['improvement_pct']:+.1f}%")
        print(f"  优胜者:      {overall['winner']}")
        
        # 子集分析
        if 'subset_analysis' in comp_results:
            print(f"\n📊 分层对比分析:")
            subset_analysis = comp_results['subset_analysis']
            
            print("| 数据集 | 样本数 | 模型AUC | 传统AUC | 提升 | 提升率 |")
            print("|--------|--------|---------|---------|------|--------|")
            
            for subset, analysis in subset_analysis.items():
                print(f"| {analysis['subset_name']} | {analysis['sample_count']} | "
                      f"{analysis['model_auc']:.4f} | {analysis['traditional_auc']:.4f} | "
                      f"{analysis['auc_improvement']:+.4f} | {analysis['improvement_pct']:+.1f}% |")
        
        return comp_results
    
    def compare_versions(self, baseline_version, current_version):
        """版本间对比"""
        print(f"\n🔄 版本演进对比: {baseline_version} → {current_version}")
        print("-" * 60)
        
        baseline_data = self.load_baseline_data(baseline_version)
        current_data = self.load_baseline_data(current_version)
        
        if not baseline_data['baseline_performance'] or not current_data['baseline_performance']:
            print("❌ 缺少性能基准数据！")
            return None
        
        baseline_perf = baseline_data['baseline_performance']['model_performance']
        current_perf = current_data['baseline_performance']['model_performance']
        
        print("📊 模型性能演进:")
        print("| 指标 | 基线值 | 当前值 | 变化 | 变化率 | 状态 |")
        print("|------|--------|--------|------|--------|------|")
        
        metrics = ['test_auc', 'test_ks', 'test_accuracy', 'test_precision', 'test_recall', 'test_f1']
        comparison = {}
        
        for metric in metrics:
            if metric in baseline_perf and metric in current_perf:
                baseline_val = baseline_perf[metric]
                current_val = current_perf[metric]
                diff = current_val - baseline_val
                diff_pct = (diff / baseline_val * 100) if baseline_val != 0 else 0
                
                status = self.get_performance_status(diff_pct)
                comparison[metric] = {
                    'baseline': baseline_val,
                    'current': current_val,
                    'diff': diff,
                    'diff_pct': diff_pct,
                    'status': status
                }
                
                print(f"| {metric.upper()} | {baseline_val:.4f} | {current_val:.4f} | "
                      f"{diff:+.4f} | {diff_pct:+.1f}% | {status} |")
        
        return comparison
    
    def get_performance_status(self, diff_pct):
        """获取性能变化状态"""
        if diff_pct > 5:
            return "🟢 显著提升"
        elif diff_pct > 2:
            return "🟢 轻微提升"
        elif diff_pct > -2:
            return "🔵 基本稳定"
        elif diff_pct > -5:
            return "🟡 轻微下降"
        else:
            return "🔴 显著下降"
    
    def plot_roc_comparison(self, version, save_path=None):
        """绘制ROC曲线对比"""
        data = self.load_baseline_data(version)
        
        if data['compare_data'] is None:
            print("❌ 未找到对比数据！")
            return
        
        df = data['compare_data']
        
        # 使用测试集数据
        if 'is_test' in df.columns:
            test_df = df[df['is_test'] == 1]
        else:
            test_df = df
        
        y_true = test_df['label']
        traditional_score = -test_df['traditional_total_score']
        model_score = test_df['model_score']
        
        plt.figure(figsize=(10, 8))
        
        # 绘制ROC曲线
        fpr_trad, tpr_trad, _ = roc_curve(y_true, traditional_score)
        fpr_model, tpr_model, _ = roc_curve(y_true, model_score)
        
        auc_trad = roc_auc_score(y_true, traditional_score)
        auc_model = roc_auc_score(y_true, model_score)
        
        plt.plot(fpr_trad, tpr_trad, 'b--', linewidth=2, 
                label=f'传统方法 (AUC={auc_trad:.3f})')
        plt.plot(fpr_model, tpr_model, 'r-', linewidth=2, 
                label=f'模型方法 (AUC={auc_model:.3f})')
        plt.plot([0, 1], [0, 1], 'k--', alpha=0.5)
        
        plt.xlabel('假正率 (False Positive Rate)')
        plt.ylabel('真正率 (True Positive Rate)')
        plt.title(f'ROC曲线对比 - 版本 {version}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📈 ROC曲线已保存到: {save_path}")
        
        return plt.gcf()
    
    def generate_comprehensive_report(self, analysis_type='all'):
        """生成综合对比报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"outputs/baseline_comparison_report_{timestamp}.md"
        
        versions = self.discover_versions()
        if len(versions) < 1:
            print("❌ 未找到任何版本！")
            return
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 基于 Baseline 的统一对比分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**分析版本**: {', '.join(versions)}\n")
            f.write(f"**数据来源**: outputs/<version>/baseline/ 目录\n\n")
            
            # 1. 单版本分析
            if analysis_type in ['all', 'single']:
                f.write("## 📊 各版本模型 vs 传统方法对比\n\n")
                
                for version in versions:
                    comp_results = self.analyze_single_version(version)
                    if comp_results:
                        overall = comp_results['overall_comparison']
                        f.write(f"### 版本 {version}\n\n")
                        f.write(f"- **模型AUC**: {overall['model_auc']:.4f}\n")
                        f.write(f"- **传统AUC**: {overall['traditional_auc']:.4f}\n")
                        f.write(f"- **提升幅度**: {overall['improvement_pct']:+.1f}%\n")
                        f.write(f"- **优胜者**: {overall['winner']}\n\n")
            
            # 2. 版本间对比
            if analysis_type in ['all', 'version'] and len(versions) >= 2:
                f.write("## 🔄 版本演进分析\n\n")
                current_version = versions[0]
                baseline_version = versions[1]
                
                comparison = self.compare_versions(baseline_version, current_version)
                if comparison:
                    f.write(f"**对比版本**: {baseline_version} → {current_version}\n\n")
                    f.write("| 指标 | 基线值 | 当前值 | 变化 | 变化率 | 状态 |\n")
                    f.write("|------|--------|--------|------|--------|------|\n")
                    
                    for metric, data in comparison.items():
                        f.write(f"| {metric.upper()} | {data['baseline']:.4f} | "
                               f"{data['current']:.4f} | {data['diff']:+.4f} | "
                               f"{data['diff_pct']:+.1f}% | {data['status']} |\n")
                f.write("\n")
            
            f.write("## 💡 数据来源说明\n\n")
            f.write("本报告基于以下 baseline 数据生成：\n")
            f.write("- `baseline_performance.json`: 模型性能基准\n")
            f.write("- `comparison_results.json`: 详细对比分析\n")
            f.write("- `model_scorecard_compare.csv`: 样本级对比数据\n\n")
            f.write("这些数据由 `run_pipeline.py` 自动生成，整合了 evaluation/ 目录下的所有对比相关信息。\n\n")
            
            f.write("---\n*报告由基于 baseline 的统一对比系统自动生成*\n")
        
        print(f"📄 综合对比报告已保存: {report_path}")
        return report_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='基于 baseline 目录的统一对比分析')
    parser.add_argument('--type', choices=['all', 'single', 'version'], 
                       default='all', help='分析类型')
    parser.add_argument('--version', help='指定分析的版本')
    parser.add_argument('--plot', action='store_true', help='生成ROC曲线图')
    args = parser.parse_args()
    
    print("🔍 基于 Baseline 的统一对比分析系统")
    print("=" * 60)
    
    analyzer = BaselineComparison()
    
    if args.version:
        # 分析指定版本
        analyzer.analyze_single_version(args.version)
        if args.plot:
            save_path = f"outputs/roc_comparison_{args.version}.png"
            analyzer.plot_roc_comparison(args.version, save_path)
    else:
        # 生成综合报告
        report_path = analyzer.generate_comprehensive_report(args.type)
        print(f"\n✅ 分析完成！")
        print(f"📄 详细报告: {report_path}")

if __name__ == "__main__":
    main()
