#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量分析脚本
分析企业风险评估样本数据的分布和特征相关性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import argparse
import matplotlib.pyplot as plt
import seaborn as sns
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

from data_analysis_utils import (
    load_data, analyze_label_distribution, categorize_features,
    calculate_feature_correlations, check_data_quality, analyze_risk_stratification, print_separator, print_correlation_analysis
)

def main():
    parser = argparse.ArgumentParser(description='数据质量分析')
    parser.add_argument('--show', action='store_true', help='是否弹窗显示图表')
    parser.add_argument('--output_dir', type=str, default='outputs/sample_data/visualization', help='图片输出目录')
    args = parser.parse_args()
    output_dir = args.output_dir
    os.makedirs(output_dir, exist_ok=True)
    print("正在加载数据...")
    
    try:
        # 读取数据
        df = load_data()
        
        # 1. 标签分布分析
        print_separator("1. 标签分布分析")
        label_info = analyze_label_distribution(df)
        print("标签计数:")
        print(label_info['counts'])
        print("\n标签比例:")
        print(label_info['proportions'])
        
        # 2. 综合风险评分统计
        print_separator("2. 综合风险评分统计")
        if 'comprehensive_risk_score' in df.columns:
            print(df['comprehensive_risk_score'].describe())
        else:
            print("未找到 comprehensive_risk_score 字段")
        
        # 3. 特征与标签相关性分析
        print_separator("3. 特征与标签相关性分析")
        feature_groups = categorize_features(df)
        for group, cols in feature_groups.items():
            correlations = calculate_feature_correlations(df, cols)
            print_correlation_analysis(correlations, f"{group}指标", max_display=10)
        
        # 4. 数据质量检查
        print_separator("4. 数据质量检查")
        quality = check_data_quality(df)
        print("缺失值统计:")
        if len(quality['missing_data']) > 0:
            print(quality['missing_data'])
        else:
            print("无缺失值")
        print(f"\n数值特征基本统计:")
        print(f"数值型特征数量: {quality['numeric_features_count']}")
        print("\n异常值检查 (绝对值>10的特征):")
        if quality['outliers']:
            for col, max_val in quality['outliers'].items():
                print(f"  {col}: 最大绝对值 = {max_val:.2f}")
        else:
            print("无明显异常值")
        
        # 5. 综合风险得分与标签关系
        print_separator("5. 综合风险得分与标签关系")
        if 'comprehensive_risk_score' in df.columns:
            risk_corr = df[['comprehensive_risk_score', 'label']].corr().iloc[0,1]
            print(f"综合风险得分与标签相关性: {risk_corr:.4f}")
            risk_analysis = analyze_risk_stratification(df)
            print("\n按风险分层的标签分布:")
            print(risk_analysis)
        
        print("\n分析完成!")
        
        # ===== 可视化部分 =====
        print("\n正在生成可视化图表...")
        # 标签分布
        plt.figure(figsize=(6,4))
        sns.countplot(x='label', data=df)
        plt.title('标签分布')
        plt.xlabel('标签（0=好企业，1=坏企业）')
        plt.ylabel('数量')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'label_distribution_cn.png'), dpi=120)
        if args.show:
            plt.show()
        plt.close()
        # 综合风险分布
        if 'comprehensive_risk_score' in df.columns:
            plt.figure(figsize=(7,4))
            sns.histplot(df['comprehensive_risk_score'], bins=30, kde=True)
            plt.title('综合风险得分分布')
            plt.xlabel('综合风险得分')
            plt.ylabel('频数')
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'risk_score_distribution_cn.png'), dpi=120)
            if args.show:
                plt.show()
            plt.close()
        # 数值特征相关性热力图
        plt.figure(figsize=(10,8))
        numeric_cols = df.select_dtypes(include=['number']).columns
        corr_matrix = df[numeric_cols].corr()
        sns.heatmap(corr_matrix, cmap='coolwarm', center=0)
        plt.title('数值特征相关性热力图')
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'feature_correlation_heatmap_cn.png'), dpi=120)
        if args.show:
            plt.show()
        plt.close()
        print(f"\n所有图表已保存到 {output_dir}/ 目录。")
        
    except Exception as e:
        print(f"分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 