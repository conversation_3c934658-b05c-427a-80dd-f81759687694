#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用增强特征重新训练模型
配合 feature_engineering_enhancement.py 使用，实现完整的特征工程->模型训练流程
"""

import sys
import os
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score, classification_report
from sklearn.preprocessing import StandardScaler
import joblib
import warnings
warnings.filterwarnings('ignore')

def load_enhanced_data():
    """加载增强后的特征数据"""
    enhanced_path = "data/enterprise_risk_sample_data_enhanced.csv"
    original_path = "data/enterprise_risk_sample_data.csv"
    
    if os.path.exists(enhanced_path):
        print(f"📊 加载增强特征数据: {enhanced_path}")
        df = pd.read_csv(enhanced_path)
        print(f"  ✅ 数据形状: {df.shape}")
        return df, True
    elif os.path.exists(original_path):
        print(f"⚠️ 未找到增强数据，使用原始数据: {original_path}")
        df = pd.read_csv(original_path)
        print(f"  📊 数据形状: {df.shape}")
        return df, False
    else:
        print("❌ 未找到任何数据文件！")
        return None, False

def prepare_features(df, use_enhanced=True):
    """准备训练特征"""
    print("\n🔧 准备训练特征...")
    
    # 排除非特征列
    exclude_cols = ['label', 'enterprise_id', 'enterprise_name', 'data_generate_time']
    if 'traditional_total_score' in df.columns:
        exclude_cols.append('traditional_total_score')
    if 'comprehensive_risk_score' in df.columns:
        exclude_cols.append('comprehensive_risk_score')

    # 排除所有非数值列
    feature_cols = []
    for col in df.columns:
        if col not in exclude_cols:
            # 检查是否为数值类型
            if pd.api.types.is_numeric_dtype(df[col]):
                feature_cols.append(col)
            else:
                print(f"  ⚠️ 跳过非数值列: {col} (类型: {df[col].dtype})")
    
    if use_enhanced:
        # 优先使用高重要性的增强特征
        enhanced_features = [
            'enterprise_health_score', 'innovation_drive_index', 'financial_strength',
            'growth_market_interaction', 'external_adaptation', 'risk_adjusted_return'
        ]
        
        # 检查哪些增强特征存在
        available_enhanced = [f for f in enhanced_features if f in df.columns]
        if available_enhanced:
            print(f"  ✅ 使用 {len(available_enhanced)} 个增强特征")
            # 将增强特征放在前面
            feature_cols = available_enhanced + [f for f in feature_cols if f not in available_enhanced]
        else:
            print("  ⚠️ 未找到增强特征，使用原始特征")
    
    X = df[feature_cols]
    y = df['label']
    
    print(f"  📊 特征数量: {len(feature_cols)}")
    print(f"  📊 样本数量: {len(X)}")
    print(f"  📊 正样本比例: {y.mean():.3f}")
    
    return X, y, feature_cols

def train_models(X, y, feature_cols):
    """训练多个模型并对比"""
    print("\n🚀 开始模型训练...")
    
    # 分割数据
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    # 特征标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    models = {
        'LogisticRegression': LogisticRegression(random_state=42, max_iter=1000),
        'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42)
    }
    
    results = {}
    
    for name, model in models.items():
        print(f"\n📈 训练 {name}...")
        
        # 训练模型
        if name == 'LogisticRegression':
            model.fit(X_train_scaled, y_train)
            y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
        else:
            model.fit(X_train, y_train)
            y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # 评估性能
        train_score = model.score(X_train_scaled if name == 'LogisticRegression' else X_train, y_train)
        test_auc = roc_auc_score(y_test, y_pred_proba)
        
        # 交叉验证
        cv_scores = cross_val_score(
            model, X_train_scaled if name == 'LogisticRegression' else X_train, 
            y_train, cv=5, scoring='roc_auc'
        )
        
        results[name] = {
            'model': model,
            'scaler': scaler if name == 'LogisticRegression' else None,
            'train_accuracy': train_score,
            'test_auc': test_auc,
            'cv_auc_mean': cv_scores.mean(),
            'cv_auc_std': cv_scores.std(),
            'y_pred_proba': y_pred_proba,
            'overfitting': train_score - test_auc
        }
        
        print(f"  训练准确率: {train_score:.4f}")
        print(f"  测试AUC: {test_auc:.4f}")
        print(f"  交叉验证AUC: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"  过拟合程度: {train_score - test_auc:.4f}")
    
    return results, X_test, y_test

def analyze_feature_importance(results, feature_cols):
    """分析特征重要性"""
    print("\n🔍 分析特征重要性...")
    
    importance_results = {}
    
    for name, result in results.items():
        model = result['model']
        
        if hasattr(model, 'feature_importances_'):
            # 随机森林等基于树的模型
            importances = model.feature_importances_
            importance_type = "Gini重要性"
        elif hasattr(model, 'coef_'):
            # 逻辑回归
            importances = np.abs(model.coef_[0])
            importance_type = "系数绝对值"
        else:
            continue
        
        # 创建特征重要性DataFrame
        feature_importance = pd.DataFrame({
            'feature': feature_cols,
            'importance': importances
        }).sort_values('importance', ascending=False)
        
        importance_results[name] = {
            'data': feature_importance,
            'type': importance_type
        }
        
        print(f"\n📊 {name} 特征重要性 ({importance_type}):")
        for i, row in feature_importance.head(10).iterrows():
            print(f"  {row['feature']:<30} {row['importance']:.4f}")
    
    return importance_results

def save_results(results, importance_results, feature_cols, use_enhanced):
    """保存训练结果"""
    print("\n💾 保存训练结果...")
    
    # 创建输出目录
    output_dir = "outputs/retrain_results"
    os.makedirs(output_dir, exist_ok=True)
    
    # 保存最佳模型
    best_model_name = max(results.keys(), key=lambda k: results[k]['test_auc'])
    best_result = results[best_model_name]
    
    model_path = os.path.join(output_dir, f"best_model_{best_model_name.lower()}.pkl")
    joblib.dump({
        'model': best_result['model'],
        'scaler': best_result['scaler'],
        'feature_cols': feature_cols,
        'model_type': best_model_name
    }, model_path)
    
    print(f"  ✅ 最佳模型已保存: {model_path}")
    
    # 生成训练报告
    report_path = os.path.join(output_dir, "retrain_report.md")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# 模型重训练报告\n\n")
        f.write(f"**生成时间**: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"**使用增强特征**: {'是' if use_enhanced else '否'}\n")
        f.write(f"**特征数量**: {len(feature_cols)}\n\n")
        
        # 模型性能对比
        f.write("## 📊 模型性能对比\n\n")
        f.write("| 模型 | 训练准确率 | 测试AUC | 交叉验证AUC | 过拟合程度 |\n")
        f.write("|------|------------|---------|-------------|------------|\n")
        
        for name, result in results.items():
            f.write(f"| {name} | {result['train_accuracy']:.4f} | {result['test_auc']:.4f} | "
                   f"{result['cv_auc_mean']:.4f}±{result['cv_auc_std']:.4f} | {result['overfitting']:.4f} |\n")
        
        # 最佳模型
        f.write(f"\n## 🏆 最佳模型: {best_model_name}\n\n")
        f.write(f"- **测试AUC**: {best_result['test_auc']:.4f}\n")
        f.write(f"- **交叉验证AUC**: {best_result['cv_auc_mean']:.4f} ± {best_result['cv_auc_std']:.4f}\n")
        f.write(f"- **过拟合程度**: {best_result['overfitting']:.4f}\n\n")
        
        # 特征重要性
        if importance_results:
            f.write("## 🔍 特征重要性分析\n\n")
            for name, imp_result in importance_results.items():
                f.write(f"### {name} ({imp_result['type']})\n\n")
                f.write("| 排名 | 特征名称 | 重要性 |\n")
                f.write("|------|----------|--------|\n")
                
                for i, row in imp_result['data'].head(15).iterrows():
                    f.write(f"| {len(imp_result['data']) - i} | {row['feature']} | {row['importance']:.4f} |\n")
                f.write("\n")
        
        # 建议
        f.write("## 💡 优化建议\n\n")
        if best_result['overfitting'] > 0.1:
            f.write("- ⚠️ **过拟合风险**: 建议增加正则化或减少特征数量\n")
        if best_result['test_auc'] < 0.75:
            f.write("- 📈 **性能提升**: 考虑更多特征工程或尝试其他算法\n")
        if use_enhanced:
            f.write("- ✅ **特征工程有效**: 增强特征提升了模型性能\n")
        
        f.write("\n---\n*报告由 retrain_with_enhanced_features.py 自动生成*\n")
    
    print(f"  📄 训练报告已保存: {report_path}")
    
    return model_path, report_path

def main():
    print("🚀 使用增强特征重新训练模型")
    print("=" * 50)
    
    # 1. 加载数据
    df, use_enhanced = load_enhanced_data()
    if df is None:
        return
    
    # 2. 准备特征
    X, y, feature_cols = prepare_features(df, use_enhanced)
    
    # 3. 训练模型
    results, X_test, y_test = train_models(X, y, feature_cols)
    
    # 4. 分析特征重要性
    importance_results = analyze_feature_importance(results, feature_cols)
    
    # 5. 保存结果
    model_path, report_path = save_results(results, importance_results, feature_cols, use_enhanced)
    
    # 6. 总结
    best_model_name = max(results.keys(), key=lambda k: results[k]['test_auc'])
    best_auc = results[best_model_name]['test_auc']
    
    print(f"\n🎉 训练完成！")
    print(f"📊 最佳模型: {best_model_name}")
    print(f"📈 测试AUC: {best_auc:.4f}")
    print(f"💾 模型文件: {model_path}")
    print(f"📄 详细报告: {report_path}")
    
    if use_enhanced:
        print("\n💡 下一步建议:")
        print("  1. 查看训练报告分析特征重要性")
        print("  2. 使用新模型重新运行 compare_scorecard_performance.py")
        print("  3. 对比增强前后的性能提升")

if __name__ == "__main__":
    main()
