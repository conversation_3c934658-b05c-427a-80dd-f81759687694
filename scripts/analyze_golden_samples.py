#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黄金样本分析脚本
基于 stage1.md 对话结果生成的分析脚本
分析模型比传统分数判断更准确的"黄金样本"
"""

import sys
import os
import glob
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve
import argparse

def find_latest_compare_csv():
    """查找最新版本的对比数据文件"""
    outputs_dir = "outputs"
    if not os.path.exists(outputs_dir):
        print("❌ outputs目录不存在！")
        return None, None
        
    # 获取所有版本号文件夹
    version_dirs = [d for d in os.listdir(outputs_dir) 
                   if os.path.isdir(os.path.join(outputs_dir, d)) and not d.startswith('.')]
    # 过滤掉非版本号文件夹
    version_dirs = [d for d in version_dirs if any(c.isdigit() for c in d)]
    
    if not version_dirs:
        print("❌ 未找到任何版本输出目录！")
        return None, None
        
    # 按版本号排序（假设语义化版本）
    version_dirs.sort(key=lambda s: [int(x) for x in s.split('.') if x.isdigit()], reverse=True)
    
    for v in version_dirs:
        compare_path = os.path.join(outputs_dir, v, "evaluation", "model_scorecard_compare.csv")
        if os.path.exists(compare_path):
            return compare_path, v
            
    print("❌ 未找到任何 compare.csv 文件！")
    return None, None

def find_optimal_threshold(y_true, y_score):
    """使用约登指数找到最优阈值"""
    fpr, tpr, thresholds = roc_curve(y_true, y_score)
    youden = tpr - fpr
    best_idx = np.argmax(youden)
    return thresholds[best_idx]

def analyze_golden_samples(df, subset_name="测试集"):
    """分析黄金样本 - 模型正确但传统分数错误的样本"""
    print(f"\n=== {subset_name} 黄金样本分析 ===")
    
    # 检查必要字段
    required_cols = ['label', 'traditional_total_score', 'model_score']
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        print(f"❌ 缺少必要字段: {missing_cols}")
        return None
    
    y_true = df['label']
    traditional_score = -df['traditional_total_score']  # 取负值，分数越高风险越高
    model_score = df['model_score']
    
    # 找到最优阈值
    trad_threshold = find_optimal_threshold(y_true, traditional_score)
    model_threshold = find_optimal_threshold(y_true, model_score)
    
    print(f"传统分数最优阈值: {trad_threshold:.4f}")
    print(f"模型分数最优阈值: {model_threshold:.4f}")
    
    # 生成预测结果
    trad_pred = (traditional_score >= trad_threshold).astype(int)
    model_pred = (model_score >= model_threshold).astype(int)
    
    # 分析四种情况
    both_correct = (trad_pred == y_true) & (model_pred == y_true)
    both_wrong = (trad_pred != y_true) & (model_pred != y_true)
    model_correct_trad_wrong = (model_pred == y_true) & (trad_pred != y_true)
    trad_correct_model_wrong = (trad_pred == y_true) & (model_pred != y_true)
    
    print(f"\n📊 判断结果统计:")
    print(f"  两者都正确: {both_correct.sum()} 个样本")
    print(f"  两者都错误: {both_wrong.sum()} 个样本")
    print(f"  模型正确，传统错误: {model_correct_trad_wrong.sum()} 个样本 ⭐")
    print(f"  传统正确，模型错误: {trad_correct_model_wrong.sum()} 个样本")
    
    # 提取黄金样本
    golden_samples = df[model_correct_trad_wrong].copy()
    if len(golden_samples) > 0:
        golden_samples['trad_pred'] = trad_pred[model_correct_trad_wrong]
        golden_samples['model_pred'] = model_pred[model_correct_trad_wrong]
        
        print(f"\n🏆 黄金样本详情 (前10个):")
        print("=" * 100)
        display_cols = ['enterprise_id', 'traditional_total_score', 'model_score', 'label', 'trad_pred', 'model_pred']
        available_cols = [col for col in display_cols if col in golden_samples.columns]
        
        if 'enterprise_id' not in golden_samples.columns:
            golden_samples['enterprise_id'] = [f'ENT_{i:06d}' for i in range(len(golden_samples))]
            available_cols = ['enterprise_id'] + [col for col in available_cols if col != 'enterprise_id']
        
        print(golden_samples[available_cols].head(10).to_string(index=False))
        
        # 分析黄金样本特征
        print(f"\n📈 黄金样本特征分析:")
        print(f"  传统分数范围: {golden_samples['traditional_total_score'].min():.2f} - {golden_samples['traditional_total_score'].max():.2f}")
        print(f"  传统分数均值: {golden_samples['traditional_total_score'].mean():.2f}")
        print(f"  模型分数范围: {golden_samples['model_score'].min():.4f} - {golden_samples['model_score'].max():.4f}")
        print(f"  模型分数均值: {golden_samples['model_score'].mean():.4f}")
        
        return {
            'golden_samples': golden_samples,
            'stats': {
                'both_correct': both_correct.sum(),
                'both_wrong': both_wrong.sum(),
                'model_correct_trad_wrong': model_correct_trad_wrong.sum(),
                'trad_correct_model_wrong': trad_correct_model_wrong.sum(),
                'total_samples': len(df)
            },
            'thresholds': {
                'traditional': trad_threshold,
                'model': model_threshold
            }
        }
    else:
        print("❌ 未找到黄金样本！")
        return None

def save_analysis_results(results, output_dir, subset_name):
    """保存分析结果到文件"""
    if not results:
        return
        
    # 保存黄金样本详情
    golden_samples_path = os.path.join(output_dir, f"golden_samples_{subset_name.lower()}.csv")
    results['golden_samples'].to_csv(golden_samples_path, index=False, encoding='utf-8')
    print(f"💾 黄金样本详情已保存到: {golden_samples_path}")
    
    # 保存统计报告
    report_path = os.path.join(output_dir, f"golden_samples_analysis_{subset_name.lower()}.txt")
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(f"黄金样本分析报告 - {subset_name}\n")
        f.write("=" * 50 + "\n\n")
        
        f.write("📊 判断结果统计:\n")
        stats = results['stats']
        f.write(f"  总样本数: {stats['total_samples']}\n")
        f.write(f"  两者都正确: {stats['both_correct']} 个样本 ({stats['both_correct']/stats['total_samples']*100:.1f}%)\n")
        f.write(f"  两者都错误: {stats['both_wrong']} 个样本 ({stats['both_wrong']/stats['total_samples']*100:.1f}%)\n")
        f.write(f"  模型正确，传统错误: {stats['model_correct_trad_wrong']} 个样本 ({stats['model_correct_trad_wrong']/stats['total_samples']*100:.1f}%) ⭐\n")
        f.write(f"  传统正确，模型错误: {stats['trad_correct_model_wrong']} 个样本 ({stats['trad_correct_model_wrong']/stats['total_samples']*100:.1f}%)\n\n")
        
        f.write("🎯 最优阈值:\n")
        f.write(f"  传统分数阈值: {results['thresholds']['traditional']:.4f}\n")
        f.write(f"  模型分数阈值: {results['thresholds']['model']:.4f}\n\n")
        
        if len(results['golden_samples']) > 0:
            f.write("🏆 黄金样本特征:\n")
            gs = results['golden_samples']
            f.write(f"  传统分数范围: {gs['traditional_total_score'].min():.2f} - {gs['traditional_total_score'].max():.2f}\n")
            f.write(f"  传统分数均值: {gs['traditional_total_score'].mean():.2f}\n")
            f.write(f"  模型分数范围: {gs['model_score'].min():.4f} - {gs['model_score'].max():.4f}\n")
            f.write(f"  模型分数均值: {gs['model_score'].mean():.4f}\n\n")
            
            f.write("💡 结论:\n")
            f.write(f"  模型在 {stats['model_correct_trad_wrong']} 个样本上表现优于传统方法，\n")
            f.write(f"  而传统方法仅在 {stats['trad_correct_model_wrong']} 个样本上表现更好。\n")
            f.write(f"  模型优势比例: {stats['model_correct_trad_wrong']/(stats['model_correct_trad_wrong']+stats['trad_correct_model_wrong'])*100:.1f}%\n")
    
    print(f"📄 分析报告已保存到: {report_path}")

def main():
    parser = argparse.ArgumentParser(description='黄金样本分析 - 找出模型优于传统方法的样本')
    parser.add_argument('--subset', choices=['all', 'train', 'test'], default='test', 
                       help='分析的数据子集 (默认: test)')
    args = parser.parse_args()
    
    print("\n=== 黄金样本分析工具 ===")
    print("基于 stage1.md 对话结果，分析模型比传统分数判断更准确的样本\n")
    
    # 查找最新的对比数据
    compare_path, version = find_latest_compare_csv()
    if not compare_path:
        print("❌ 未找到对比数据文件！")
        return
    
    print(f"📂 读取数据: {compare_path} (版本: {version})")
    df = pd.read_csv(compare_path)
    
    # 根据参数选择数据子集
    subset_mapping = {'all': '全量样本', 'train': '训练集', 'test': '测试集'}
    subset_name = subset_mapping[args.subset]
    
    if args.subset != 'all' and 'is_test' in df.columns:
        if args.subset == 'test':
            df_subset = df[df['is_test'] == 1].copy()
        else:  # train
            df_subset = df[df['is_test'] == 0].copy()
    else:
        df_subset = df.copy()
    
    print(f"📊 分析数据集: {subset_name} ({len(df_subset)} 个样本)")
    
    # 执行黄金样本分析
    results = analyze_golden_samples(df_subset, subset_name)
    
    if results:
        # 保存结果
        output_dir = os.path.dirname(compare_path)
        save_analysis_results(results, output_dir, args.subset)
        
        print(f"\n✅ 分析完成！发现 {results['stats']['model_correct_trad_wrong']} 个黄金样本")
        print(f"   模型优势明显，在争议样本中胜率: {results['stats']['model_correct_trad_wrong']/(results['stats']['model_correct_trad_wrong']+results['stats']['trad_correct_model_wrong'])*100:.1f}%")
    else:
        print("❌ 分析失败！")

if __name__ == "__main__":
    main()
