#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一对比管理系统
整合所有对比逻辑，自动从 outputs/<version>/baseline/ 读取数据进行对比
解决多个对比脚本功能重合的问题，提供清晰的对比架构
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime
from pathlib import Path
import argparse

class UnifiedComparisonManager:
    """统一对比管理器"""
    
    def __init__(self):
        self.outputs_dir = Path("outputs")
        self.versions = []
        
    def discover_versions(self):
        """发现所有可用版本"""
        if not self.outputs_dir.exists():
            print("❌ outputs目录不存在！")
            return []
        
        # 获取所有版本目录
        version_dirs = []
        for d in self.outputs_dir.iterdir():
            if d.is_dir() and not d.name.startswith('.'):
                # 检查是否有baseline目录
                baseline_dir = d / "baseline"
                if baseline_dir.exists():
                    version_dirs.append(d.name)
        
        # 按版本号排序
        def version_key(v):
            try:
                if '_enhanced' in v:
                    base_v = v.replace('_enhanced', '')
                    parts = [int(x) for x in base_v.split('.')]
                    return parts + [1]  # enhanced 版本排在后面
                else:
                    parts = [int(x) for x in v.split('.')]
                    return parts + [0]
            except:
                return [0, 0, 0, 0]
        
        version_dirs.sort(key=version_key, reverse=True)
        self.versions = version_dirs
        
        print(f"📂 发现版本: {', '.join(version_dirs)}")
        return version_dirs
    
    def load_baseline_data(self, version):
        """加载指定版本的baseline数据"""
        version_dir = self.outputs_dir / version
        baseline_dir = version_dir / "baseline"
        
        data = {
            'version': version,
            'baseline_performance': None,
            'baseline_summary': None,
            'compare_data': None,
            'evaluation_results': None
        }
        
        # 1. 加载 baseline_performance.json
        perf_path = baseline_dir / "baseline_performance.json"
        if perf_path.exists():
            with open(perf_path, 'r') as f:
                data['baseline_performance'] = json.load(f)
        
        # 2. 加载 baseline_summary.json
        summary_path = baseline_dir / "baseline_summary.json"
        if summary_path.exists():
            with open(summary_path, 'r') as f:
                data['baseline_summary'] = json.load(f)
        
        # 3. 加载对比数据
        compare_path = version_dir / "evaluation" / "model_scorecard_compare.csv"
        if compare_path.exists():
            data['compare_data'] = pd.read_csv(compare_path)
        
        # 4. 加载评估结果
        eval_path = version_dir / "evaluation" / "evaluation_results.json"
        if eval_path.exists():
            with open(eval_path, 'r') as f:
                data['evaluation_results'] = json.load(f)
        
        return data
    
    def compare_model_vs_traditional(self, version):
        """模型 vs 传统方法对比（单版本内部对比）"""
        print(f"\n🔍 版本 {version}: 模型 vs 传统方法对比")
        print("-" * 50)

        data = self.load_baseline_data(version)

        # 检查新格式的 baseline_summary
        if data['baseline_summary']:
            summary = data['baseline_summary']
            comparison = summary['model_vs_traditional']

            print(f"📊 模型AUC:     {comparison['model_auc']:.4f}")
            print(f"📊 传统AUC:     {comparison['traditional_auc']:.4f}")
            print(f"📈 AUC提升:     {comparison['auc_improvement']:+.4f}")
            print(f"📈 提升幅度:    {comparison['improvement_pct']:+.1f}%")
            print(f"🏆 优胜者:      {comparison['winner']}")

            return comparison

        # 检查新格式的 baseline_performance
        elif data['baseline_performance'] and 'traditional_performance' in data['baseline_performance']:
            perf = data['baseline_performance']
            model_auc = perf['model_performance']['test_auc']
            trad_auc = perf['traditional_performance']['auc']
            improvement = perf['traditional_performance']['improvement_over_traditional']
            improvement_pct = perf['traditional_performance']['improvement_pct']

            print(f"📊 模型AUC:     {model_auc:.4f}")
            print(f"📊 传统AUC:     {trad_auc:.4f}")
            print(f"📈 AUC提升:     {improvement:+.4f}")
            print(f"📈 提升幅度:    {improvement_pct:+.1f}%")
            print(f"🏆 优胜者:      {'model' if improvement > 0 else 'traditional'}")

            return {
                'model_auc': model_auc,
                'traditional_auc': trad_auc,
                'auc_improvement': improvement,
                'improvement_pct': improvement_pct,
                'winner': 'model' if improvement > 0 else 'traditional'
            }

        else:
            print("❌ 未找到baseline数据！")
            return None
    
    def compare_versions(self, baseline_version, current_version):
        """版本间对比（时间维度对比）"""
        print(f"\n🔍 版本对比: {baseline_version} vs {current_version}")
        print("-" * 50)
        
        baseline_data = self.load_baseline_data(baseline_version)
        current_data = self.load_baseline_data(current_version)
        
        if not baseline_data['baseline_performance'] or not current_data['baseline_performance']:
            print("❌ 缺少必要的baseline数据！")
            return None

        # 处理不同格式的baseline数据
        def extract_model_performance(data):
            if 'model_performance' in data:
                return data['model_performance']
            else:
                # 旧格式，直接使用根级别的指标
                return {
                    'test_auc': data.get('auc', 0),
                    'test_ks': data.get('ks', 0),
                    'test_accuracy': data.get('accuracy', 0),
                    'test_precision': data.get('precision', 0),
                    'test_recall': data.get('recall', 0),
                    'test_f1': data.get('f1', 0)
                }

        baseline_perf = extract_model_performance(baseline_data['baseline_performance'])
        current_perf = extract_model_performance(current_data['baseline_performance'])
        
        comparison = {}
        
        # 对比关键指标
        metrics = ['test_auc', 'test_ks', 'test_accuracy', 'test_precision', 'test_recall', 'test_f1']
        
        print("📊 性能指标对比:")
        for metric in metrics:
            if metric in baseline_perf and metric in current_perf:
                baseline_val = baseline_perf[metric]
                current_val = current_perf[metric]
                diff = current_val - baseline_val
                diff_pct = (diff / baseline_val * 100) if baseline_val != 0 else 0
                
                status = self.get_performance_status(diff_pct)
                comparison[metric] = {
                    'baseline': baseline_val,
                    'current': current_val,
                    'diff': diff,
                    'diff_pct': diff_pct,
                    'status': status
                }
                
                print(f"  {metric.upper():<15} {baseline_val:.4f} → {current_val:.4f} "
                      f"({diff:+.4f}, {diff_pct:+.1f}%) {status}")
        
        return comparison
    
    def get_performance_status(self, diff_pct):
        """获取性能变化状态"""
        if diff_pct > 5:
            return "🟢 显著提升"
        elif diff_pct > 2:
            return "🟢 轻微提升"
        elif diff_pct > -2:
            return "🔵 基本稳定"
        elif diff_pct > -5:
            return "🟡 轻微下降"
        else:
            return "🔴 显著下降"
    
    def analyze_golden_samples(self, version):
        """分析黄金样本（模型优于传统方法的样本）"""
        print(f"\n🏆 版本 {version}: 黄金样本分析")
        print("-" * 50)
        
        data = self.load_baseline_data(version)
        
        if data['compare_data'] is None:
            print("❌ 未找到对比数据！")
            return None
        
        df = data['compare_data']
        
        # 检查必要字段
        required_cols = ['label', 'traditional_total_score', 'model_score']
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            print(f"❌ 缺少必要字段: {missing_cols}")
            return None
        
        # 使用测试集数据
        if 'is_test' in df.columns:
            test_df = df[df['is_test'] == 1].copy()
        else:
            test_df = df.copy()
        
        print(f"📊 分析样本数: {len(test_df)}")
        
        # 计算最优阈值
        from sklearn.metrics import roc_curve
        
        y_true = test_df['label']
        traditional_score = -test_df['traditional_total_score']  # 取负值
        model_score = test_df['model_score']
        
        # 找到最优阈值
        def find_optimal_threshold(y_true, y_score):
            fpr, tpr, thresholds = roc_curve(y_true, y_score)
            youden = tpr - fpr
            best_idx = np.argmax(youden)
            return thresholds[best_idx]
        
        trad_threshold = find_optimal_threshold(y_true, traditional_score)
        model_threshold = find_optimal_threshold(y_true, model_score)
        
        # 生成预测结果
        trad_pred = (traditional_score >= trad_threshold).astype(int)
        model_pred = (model_score >= model_threshold).astype(int)
        
        # 分析四种情况
        both_correct = (trad_pred == y_true) & (model_pred == y_true)
        both_wrong = (trad_pred != y_true) & (model_pred != y_true)
        model_correct_trad_wrong = (model_pred == y_true) & (trad_pred != y_true)
        trad_correct_model_wrong = (trad_pred == y_true) & (model_pred != y_true)
        
        golden_samples = model_correct_trad_wrong.sum()
        total_disputes = model_correct_trad_wrong.sum() + trad_correct_model_wrong.sum()
        win_rate = (golden_samples / total_disputes * 100) if total_disputes > 0 else 0
        
        print(f"🏆 黄金样本数:   {golden_samples}")
        print(f"📊 争议样本数:   {total_disputes}")
        print(f"🎯 模型胜率:     {win_rate:.1f}%")
        print(f"✅ 两者都对:     {both_correct.sum()}")
        print(f"❌ 两者都错:     {both_wrong.sum()}")
        
        return {
            'golden_samples': golden_samples,
            'total_disputes': total_disputes,
            'win_rate': win_rate,
            'both_correct': both_correct.sum(),
            'both_wrong': both_wrong.sum()
        }
    
    def generate_unified_report(self, analysis_type='all'):
        """生成统一对比报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_path = f"outputs/unified_comparison_report_{timestamp}.md"
        
        versions = self.discover_versions()
        if len(versions) < 1:
            print("❌ 未找到任何版本！")
            return
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 统一对比分析报告\n\n")
            f.write(f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**分析版本**: {', '.join(versions)}\n\n")
            
            # 1. 模型 vs 传统方法对比（每个版本内部）
            if analysis_type in ['all', 'model_vs_traditional']:
                f.write("## 📊 模型 vs 传统方法对比\n\n")
                f.write("| 版本 | 模型AUC | 传统AUC | 提升幅度 | 优胜者 |\n")
                f.write("|------|---------|---------|----------|--------|\n")
                
                for version in versions:
                    comparison = self.compare_model_vs_traditional(version)
                    if comparison:
                        f.write(f"| {version} | {comparison['model_auc']:.4f} | "
                               f"{comparison['traditional_auc']:.4f} | "
                               f"{comparison['improvement_pct']:+.1f}% | "
                               f"{comparison['winner']} |\n")
                f.write("\n")
            
            # 2. 版本间对比
            if analysis_type in ['all', 'version_comparison'] and len(versions) >= 2:
                f.write("## 🔄 版本演进对比\n\n")
                current_version = versions[0]
                baseline_version = versions[1]
                
                comparison = self.compare_versions(baseline_version, current_version)
                if comparison:
                    f.write(f"**对比版本**: {baseline_version} → {current_version}\n\n")
                    f.write("| 指标 | 基线值 | 当前值 | 变化 | 变化率 | 状态 |\n")
                    f.write("|------|--------|--------|------|--------|------|\n")
                    
                    for metric, data in comparison.items():
                        f.write(f"| {metric.upper()} | {data['baseline']:.4f} | "
                               f"{data['current']:.4f} | {data['diff']:+.4f} | "
                               f"{data['diff_pct']:+.1f}% | {data['status']} |\n")
                f.write("\n")
            
            # 3. 黄金样本分析
            if analysis_type in ['all', 'golden_samples']:
                f.write("## 🏆 黄金样本分析\n\n")
                f.write("| 版本 | 黄金样本数 | 争议样本数 | 模型胜率 |\n")
                f.write("|------|------------|------------|----------|\n")
                
                for version in versions:
                    golden_analysis = self.analyze_golden_samples(version)
                    if golden_analysis:
                        f.write(f"| {version} | {golden_analysis['golden_samples']} | "
                               f"{golden_analysis['total_disputes']} | "
                               f"{golden_analysis['win_rate']:.1f}% |\n")
                f.write("\n")
            
            f.write("---\n*报告由统一对比管理系统自动生成*\n")
        
        print(f"📄 统一对比报告已保存: {report_path}")
        return report_path

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='统一对比管理系统')
    parser.add_argument('--type', choices=['all', 'model_vs_traditional', 'version_comparison', 'golden_samples'], 
                       default='all', help='对比类型')
    args = parser.parse_args()
    
    print("🔍 统一对比管理系统")
    print("=" * 50)
    
    manager = UnifiedComparisonManager()
    
    # 生成统一报告
    report_path = manager.generate_unified_report(args.type)
    
    print(f"\n✅ 对比分析完成！")
    print(f"📄 详细报告: {report_path}")

if __name__ == "__main__":
    main()
