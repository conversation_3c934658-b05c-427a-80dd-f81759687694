#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Compare Scorecard Performance
自动查找outputs/<latest_version>/evaluation/model_scorecard_compare.csv，
对比人工加权总分与模型分数对主观标签的判别力。
"""

import sys
import os
import glob
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score, roc_curve, accuracy_score, confusion_matrix, f1_score, precision_score, recall_score
import matplotlib.pyplot as plt
import argparse
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']  # 设置常见中文字体
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示为方块的问题

def find_latest_compare_csv():
    outputs_dir = "outputs"
    # 获取所有版本号文件夹
    version_dirs = [d for d in os.listdir(outputs_dir) if os.path.isdir(os.path.join(outputs_dir, d)) and not d.startswith('.')]
    # 过滤掉非版本号文件夹
    version_dirs = [d for d in version_dirs if any(c.isdigit() for c in d)]
    if not version_dirs:
        print("❌ 未找到任何版本输出目录！")
        return None
    # 按版本号排序（假设语义化版本）
    version_dirs.sort(key=lambda s: [int(x) for x in s.split('.') if x.isdigit()], reverse=True)
    for v in version_dirs:
        compare_path = os.path.join(outputs_dir, v, "evaluation", "model_scorecard_compare.csv")
        if os.path.exists(compare_path):
            return compare_path, v
    print("❌ 未找到任何 compare.csv 文件！")
    return None, None

def calc_ks(y_true, y_score):
    fpr, tpr, _ = roc_curve(y_true, y_score)
    ks = max(tpr - fpr)
    return ks

def evaluate_score(y_true, score, threshold=None):
    auc = roc_auc_score(y_true, score)
    ks = calc_ks(y_true, score)
    if threshold is None:
        fpr, tpr, thresholds = roc_curve(y_true, score)
        youden = tpr - fpr
        best_idx = np.argmax(youden)
        threshold = thresholds[best_idx]
    y_pred = (score >= threshold).astype(int)
    acc = accuracy_score(y_true, y_pred)
    f1 = f1_score(y_true, y_pred)
    precision = precision_score(y_true, y_pred)
    recall = recall_score(y_true, y_pred)
    cm = confusion_matrix(y_true, y_pred)
    tn, fp, fn, tp = cm.ravel()
    specificity = tn / (tn + fp) if (tn + fp) > 0 else 0
    return {
        "AUC": auc,
        "KS": ks,
        "Accuracy": acc,
        "F1": f1,
        "Precision": precision,
        "Recall": recall,
        "Specificity": specificity,
        "Threshold": threshold,
        "ConfusionMatrix": cm,
        "TP": tp, "FP": fp, "TN": tn, "FN": fn
    }

def print_eval_result(name, result, file=None):
    lines = [
        f"\n📊 {name} 判别力评估:",
        f"  AUC:        {result['AUC']:.4f}",
        f"  KS值:       {result['KS']:.4f}",
        f"  准确率:     {result['Accuracy']:.4f}",
        f"  精确率:     {result['Precision']:.4f}",
        f"  召回率:     {result['Recall']:.4f}",
        f"  特异性:     {result['Specificity']:.4f}",
        f"  F1分数:     {result['F1']:.4f}",
        f"  最优阈值:   {result['Threshold']:.4f}",
        f"  混淆矩阵:   TN={result['TN']}  FP={result['FP']}  FN={result['FN']}  TP={result['TP']}"
    ]
    for line in lines:
        print(line)
        if file:
            file.write(line + "\n")

def create_comparison_table(all_results):
    """创建对比表格，将所有结果汇总到一个表格中"""
    # 准备表格数据
    table_data = []
    metrics = ['AUC', 'KS', 'Accuracy', 'Precision', 'Recall', 'Specificity', 'F1', 'Threshold']

    for subset in ['all', 'train', 'test']:
        subset_names = {'all': '全量样本', 'train': '训练集', 'test': '测试集'}
        subset_name = subset_names[subset]

        if subset in all_results:
            # 人工分数行
            if all_results[subset]['trad']:
                row_trad = [f"{subset_name}-人工分数"]
                for metric in metrics:
                    row_trad.append(f"{all_results[subset]['trad'][metric]:.4f}")
                table_data.append(row_trad)

            # 模型分数行
            if all_results[subset]['model']:
                row_model = [f"{subset_name}-模型分数"]
                for metric in metrics:
                    row_model.append(f"{all_results[subset]['model'][metric]:.4f}")
                table_data.append(row_model)

    return table_data, ['评估对象'] + metrics

def plot_roc(y_true, score, label, color):
    fpr, tpr, _ = roc_curve(y_true, score)
    auc = roc_auc_score(y_true, score)
    plt.plot(fpr, tpr, label=f"{label} (AUC={auc:.3f})", color=color)

def evaluate_all_subsets(df):
    """评估所有数据子集的性能"""
    results = {}

    for subset, subset_name in zip(['all', 'train', 'test'], ['全量样本', '训练集', '测试集']):
        if 'is_test' in df.columns:
            if subset == 'test':
                df_sub = df[df['is_test'] == 1]
            elif subset == 'train':
                df_sub = df[df['is_test'] == 0]
            else:
                df_sub = df
        else:
            df_sub = df

        if len(df_sub) == 0:
            print(f"⚠️ {subset_name} 数据为空，跳过")
            continue

        y_true = df_sub['label']
        print(f"评估对象：{subset_name} ({len(df_sub)} 个样本)")

        # 检查必要字段
        if 'traditional_total_score' not in df_sub.columns:
            print("❌ 缺少 traditional_total_score 字段！")
            continue

        # 评估传统分数
        traditional_score = -df_sub['traditional_total_score']
        result_trad = evaluate_score(y_true, traditional_score)
        print_eval_result("人工加权总分", result_trad)

        # 评估模型分数
        result_model = None
        if 'model_score' in df_sub.columns:
            model_score = df_sub['model_score']
            result_model = evaluate_score(y_true, model_score)
            print_eval_result("模型分数", result_model)

        results[subset] = {
            'trad': result_trad,
            'model': result_model,
            'y_true': y_true,
            'model_score': df_sub.get('model_score', None),
            'traditional_score': traditional_score
        }

    return results

def save_comparison_report(results, compare_path, version):
    """保存对比报告"""
    report_path = os.path.join(os.path.dirname(compare_path), "scorecard_performance_comparison_summary.txt")
    with open(report_path, "w", encoding="utf-8") as f:
        f.write(f"评分卡性能对比汇总报告 (版本: {version})\n")
        f.write("=" * 60 + "\n\n")

        # 创建对比表格
        table_data, headers = create_comparison_table(results)

        if table_data:
            f.write("📊 性能指标对比表格:\n\n")

            # 计算每列的最大宽度
            col_widths = [max(len(str(row[i])) for row in [headers] + table_data) + 2 for i in range(len(headers))]

            # 写入表头
            header_line = "│".join(f"{headers[i]:^{col_widths[i]}}" for i in range(len(headers)))
            f.write("┌" + "┬".join("─" * col_widths[i] for i in range(len(headers))) + "┐\n")
            f.write("│" + header_line + "│\n")
            f.write("├" + "┼".join("─" * col_widths[i] for i in range(len(headers))) + "┤\n")

            # 写入数据行
            for row in table_data:
                data_line = "│".join(f"{str(row[i]):^{col_widths[i]}}" for i in range(len(row)))
                f.write("│" + data_line + "│\n")

            f.write("└" + "┴".join("─" * col_widths[i] for i in range(len(headers))) + "┘\n\n")

        # 添加详细分析
        f.write("📈 详细分析:\n\n")
        for subset, subset_name in zip(['all', 'train', 'test'], ['全量样本', '训练集', '测试集']):
            if subset in results and results[subset]['trad'] and results[subset]['model']:
                f.write(f"{subset_name}:\n")
                trad_auc = results[subset]['trad']['AUC']
                model_auc = results[subset]['model']['AUC']
                auc_diff = model_auc - trad_auc
                auc_status = '模型更优' if auc_diff > 0 else '传统更优' if auc_diff < 0 else '相当'
                f.write(f"  AUC提升: {auc_diff:+.4f} ({auc_status})\n")

                trad_ks = results[subset]['trad']['KS']
                model_ks = results[subset]['model']['KS']
                ks_diff = model_ks - trad_ks
                ks_status = '模型更优' if ks_diff > 0 else '传统更优' if ks_diff < 0 else '相当'
                f.write(f"  KS值提升: {ks_diff:+.4f} ({ks_status})\n\n")

    print(f"📄 统一对比报告已保存到: {report_path}")
    return report_path

def main():
    parser = argparse.ArgumentParser(description='评分卡判别力对比')
    parser.add_argument('--show', action='store_true', help='是否弹窗显示ROC曲线')
    args = parser.parse_args()

    print("\n=== Scorecard Performance Comparison (Auto Version) ===\n")
    compare_path, version = find_latest_compare_csv()
    if not compare_path:
        print("❌ compare.csv 未找到，无法对比分析！")
        return
    print(f"读取对比数据: {compare_path}")
    df = pd.read_csv(compare_path)

    # 评估所有子集
    results = evaluate_all_subsets(df)

    # 保存对比报告
    save_comparison_report(results, compare_path, version)

    # 绘制ROC曲线对比图
    plt.figure(figsize=(10,7))
    colors = {'all':'#2ecc71', 'train':'#3498db', 'test':'#e74c3c'}
    linestyles = {'model':'-', 'trad':'--'}

    for subset, subset_name in zip(['all', 'train', 'test'], ['全量样本', '训练集', '测试集']):
        if subset in results:
            for score_type, score_label in zip(['model', 'trad'], ['模型分数', '人工分数']):
                if results[subset][score_type] is not None:
                    y_true = results[subset]['y_true']
                    if score_type == 'model':
                        score = results[subset]['model_score']
                    else:
                        score = results[subset]['traditional_score']

                    if score is not None and hasattr(score, '__len__') and len(score) == len(y_true):
                        fpr, tpr, _ = roc_curve(y_true, score)
                        auc = roc_auc_score(y_true, score)
                        plt.plot(fpr, tpr, linestyle=linestyles[score_type], color=colors[subset],
                                 label=f"{score_label}-{subset_name} (AUC={auc:.3f})")

    plt.plot([0,1],[0,1],'k--',alpha=0.5)
    plt.xlabel("False Positive Rate")
    plt.ylabel("True Positive Rate")
    plt.title(f"模型分数与人工分数ROC曲线对比 (v{version})")
    plt.legend()
    plt.tight_layout()
    roc_path = os.path.join(os.path.dirname(compare_path), "scorecard_roc_comparison_all_train_test_both.png")
    plt.savefig(roc_path, dpi=120)
    if args.show:
        plt.show()
    plt.close()
    print(f"📈 ROC曲线对比图已保存到: {roc_path}")
    print("\n✅ 对比分析完成！现在只生成一个统一的对比报告文件，而不是三个分散的txt文件。")

if __name__ == "__main__":
    main() 